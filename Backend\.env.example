# This is an example environment configuration file.
# Please replace the values with your own credentials.

MONGO_URI=your_mongo_uri_here
MONGO_DB_NAME="your_database_name_here"
PORT=5000
JWT_SECRET=your_jwt_secret_here

CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name_here
CLOUDINARY_API_KEY=your_cloudinary_api_key_here
CLOUDINARY_API_SECRET=your_cloudinary_api_secret_here

EMAIL_USER=your_email_here
EMAIL_PASS=your_email_password_here

TWILIO_ACCOUNT_SID=your_twilio_account_sid_here
TWILIO_AUTH_TOKEN=your_twilio_auth_token_here
TWILIO_PHONE_NUMBER=your_twilio_phone_number_here

NODE_ENV=development
DISABLE_SMS_DELIVERY=true # Set to true in development to skip actual SMS delivery

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000 # 15 minutes in milliseconds
RATE_LIMIT_MAX=100 # Maximum requests per window

# Production Debugging (optional)
DEBUG_PROXY=false # Set to true in production to debug proxy headers

# HTTPS Configuration (optional)
HTTPS_PORT=5443 # Port for HTTPS server if SSL certificates are available